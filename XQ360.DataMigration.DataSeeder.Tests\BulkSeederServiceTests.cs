using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using System;
using System.Threading;
using System.Threading.Tasks;
using Xunit;
using XQ360.DataMigration.Services;
using XQ360.DataMigration.Web.Hubs;
using XQ360.DataMigration.Web.Models;
using XQ360.DataMigration.Web.Services.BulkSeeder;

namespace XQ360.DataMigration.DataSeeder.Tests
{
    /// <summary>
    /// Comprehensive unit tests for BulkSeederService
    /// Tests all public methods, execution paths, edge cases, error handling, and dependency interactions
    /// </summary>
    public class BulkSeederServiceTests : IDisposable
    {
        private readonly Mock<ILogger<BulkSeederService>> _mockLogger;
        private readonly Mock<IOptions<BulkSeederConfiguration>> _mockOptions;
        private readonly Mock<ISqlDataGenerationService> _mockSqlDataService;
        private readonly Mock<IEnvironmentConfigurationService> _mockEnvironmentService;
        private readonly Mock<IHubContext<MigrationHub>> _mockHubContext;
        private readonly Mock<IApiOrchestrationService> _mockApiOrchestrationService;
        private readonly Mock<IComplexEntityCreationService> _mockComplexEntityService;
        private readonly BulkSeederConfiguration _testConfig;

        public BulkSeederServiceTests()
        {
            _mockLogger = new Mock<ILogger<BulkSeederService>>();
            _mockOptions = new Mock<IOptions<BulkSeederConfiguration>>();
            _mockSqlDataService = new Mock<ISqlDataGenerationService>();
            _mockEnvironmentService = new Mock<IEnvironmentConfigurationService>();
            _mockHubContext = new Mock<IHubContext<MigrationHub>>();
            _mockApiOrchestrationService = new Mock<IApiOrchestrationService>();
            _mockComplexEntityService = new Mock<IComplexEntityCreationService>();

            _testConfig = TestConfigurationHelper.GetBulkSeederConfiguration();
            _mockOptions.Setup(x => x.Value).Returns(_testConfig);

            // Setup environment service
            var testEnvironment = TestConfigurationHelper.GetTestEnvironment();
            var testMigrationConfig = TestConfigurationHelper.GetTestConfiguration();
            _mockEnvironmentService.Setup(x => x.CurrentEnvironment).Returns(testEnvironment);
            _mockEnvironmentService.Setup(x => x.CurrentMigrationConfiguration).Returns(testMigrationConfig);

            // Setup SignalR hub context
            var mockClients = new Mock<IHubClients>();
            var mockClientProxy = new Mock<IClientProxy>();
            _mockHubContext.Setup(x => x.Clients).Returns(mockClients.Object);
            mockClients.Setup(x => x.All).Returns(mockClientProxy.Object);
        }

        #region Constructor Tests

        [Fact]
        public void Constructor_WithValidParameters_ShouldCreateInstance()
        {
            // Act
            var service = CreateBulkSeederService();

            // Assert
            Assert.NotNull(service);
        }

        [Fact]
        public void Constructor_WithNullLogger_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new BulkSeederService(
                null!,
                _mockOptions.Object,
                _mockSqlDataService.Object,
                _mockEnvironmentService.Object,
                _mockHubContext.Object,
                _mockApiOrchestrationService.Object,
                _mockComplexEntityService.Object));
        }

        [Fact]
        public void Constructor_WithNullOptions_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new BulkSeederService(
                _mockLogger.Object,
                null!,
                _mockSqlDataService.Object,
                _mockEnvironmentService.Object,
                _mockHubContext.Object,
                _mockApiOrchestrationService.Object,
                _mockComplexEntityService.Object));
        }

        [Fact]
        public void Constructor_WithNullSqlDataService_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new BulkSeederService(
                _mockLogger.Object,
                _mockOptions.Object,
                null!,
                _mockEnvironmentService.Object,
                _mockHubContext.Object,
                _mockApiOrchestrationService.Object,
                _mockComplexEntityService.Object));
        }

        #endregion

        #region ExecuteSeederAsync Tests

        [Fact]
        public async Task ExecuteSeederAsync_WithValidOptions_ShouldReturnSuccessResult()
        {
            // Arrange
            var service = CreateBulkSeederService();
            var options = TestConfigurationHelper.CreateTestSeederOptions();

            SetupSuccessfulDataGeneration();

            // Act
            var result = await service.ExecuteSeederAsync(options);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Success);
            Assert.NotEmpty(result.SessionId);
            Assert.True(result.Duration > TimeSpan.Zero);
        }

        [Fact]
        public async Task ExecuteSeederAsync_WithDriversCount_ShouldCallGenerateDriverDataAsync()
        {
            // Arrange
            var service = CreateBulkSeederService();
            var options = TestConfigurationHelper.CreateTestSeederOptions(driversCount: 50);

            SetupSuccessfulDataGeneration();

            // Act
            await service.ExecuteSeederAsync(options);

            // Assert
            _mockSqlDataService.Verify(x => x.GenerateDriverDataAsync(
                It.IsAny<Guid>(),
                50,
                It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task ExecuteSeederAsync_WithVehiclesCount_ShouldCallGenerateVehicleDataAsync()
        {
            // Arrange
            var service = CreateBulkSeederService();
            var options = TestConfigurationHelper.CreateTestSeederOptions(vehiclesCount: 25);

            SetupSuccessfulDataGeneration();

            // Act
            await service.ExecuteSeederAsync(options);

            // Assert
            _mockSqlDataService.Verify(x => x.GenerateVehicleDataAsync(
                It.IsAny<Guid>(),
                25,
                It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task ExecuteSeederAsync_WithDryRunFalse_ShouldCallProcessStagedDataAsync()
        {
            // Arrange
            var service = CreateBulkSeederService();
            var options = TestConfigurationHelper.CreateTestSeederOptions(dryRun: false);

            SetupSuccessfulDataGeneration();

            // Act
            await service.ExecuteSeederAsync(options);

            // Assert
            _mockSqlDataService.Verify(x => x.ProcessStagedDataAsync(
                It.IsAny<Guid>(),
                false,
                It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task ExecuteSeederAsync_WithDryRunTrue_ShouldNotCallProcessStagedDataAsync()
        {
            // Arrange
            var service = CreateBulkSeederService();
            var options = TestConfigurationHelper.CreateTestSeederOptions(dryRun: true);

            SetupSuccessfulDataGeneration();

            // Act
            await service.ExecuteSeederAsync(options);

            // Assert
            _mockSqlDataService.Verify(x => x.ProcessStagedDataAsync(
                It.IsAny<Guid>(),
                It.IsAny<bool>(),
                It.IsAny<CancellationToken>()), Times.Never);
        }

        #endregion

        #region Error Handling Tests

        [Fact]
        public async Task ExecuteSeederAsync_WhenSqlDataServiceThrows_ShouldReturnFailureResult()
        {
            // Arrange
            var service = CreateBulkSeederService();
            var options = TestConfigurationHelper.CreateTestSeederOptions();

            _mockSqlDataService.Setup(x => x.GenerateDriverDataAsync(
                It.IsAny<Guid>(),
                It.IsAny<int>(),
                It.IsAny<CancellationToken>()))
                .ThrowsAsync(new InvalidOperationException("Database connection failed"));

            // Act
            var result = await service.ExecuteSeederAsync(options);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.Success);
            Assert.Contains("Database connection failed", result.Summary);
            Assert.NotEmpty(result.Errors);
        }

        [Fact]
        public async Task ExecuteSeederAsync_WhenProcessingFails_ShouldReturnFailureResult()
        {
            // Arrange
            var service = CreateBulkSeederService();
            var options = TestConfigurationHelper.CreateTestSeederOptions(dryRun: false);

            SetupSuccessfulDataGeneration();
            _mockSqlDataService.Setup(x => x.ProcessStagedDataAsync(
                It.IsAny<Guid>(),
                It.IsAny<bool>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(new ProcessingResult
                {
                    Success = false,
                    ProcessingErrors = new List<string> { "Processing failed" }
                });

            // Act
            var result = await service.ExecuteSeederAsync(options);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.Success);
            Assert.Contains("Processing failed", result.Summary);
        }

        #endregion

        #region Edge Cases and Cancellation Tests

        [Fact]
        public async Task ExecuteSeederAsync_WithZeroCounts_ShouldThrowArgumentException()
        {
            // Arrange
            var service = CreateBulkSeederService();
            var options = TestConfigurationHelper.CreateTestSeederOptions(driversCount: 0, vehiclesCount: 0);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<ArgumentException>(() => 
                service.ExecuteSeederAsync(options));
            
            Assert.Contains("must be positive", exception.Message);
        }

        [Fact]
        public async Task ExecuteSeederAsync_WithNullCounts_ShouldUseDefaults()
        {
            // Arrange
            var service = CreateBulkSeederService();
            var options = TestConfigurationHelper.CreateTestSeederOptions(driversCount: null, vehiclesCount: null);

            // Setup successful mock responses
            SetupSuccessfulDataGeneration();

            // Act
            var result = await service.ExecuteSeederAsync(options);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Success);
            // Should use default values from configuration
        }

        [Fact]
        public async Task ExecuteSeederAsync_WithCancellationToken_ShouldPassTokenToServices()
        {
            // Arrange
            var service = CreateBulkSeederService();
            var options = TestConfigurationHelper.CreateTestSeederOptions();
            var cancellationTokenSource = new CancellationTokenSource();
            var cancellationToken = cancellationTokenSource.Token;

            SetupSuccessfulDataGeneration();

            // Act
            await service.ExecuteSeederAsync(options, cancellationToken);

            // Assert
            _mockSqlDataService.Verify(x => x.GenerateDriverDataAsync(
                It.IsAny<Guid>(),
                It.IsAny<int>(),
                cancellationToken), Times.Once);
        }

        [Fact]
        public async Task ExecuteSeederAsync_WhenCancelled_ShouldThrowOperationCancelledException()
        {
            // Arrange
            var service = CreateBulkSeederService();
            var options = TestConfigurationHelper.CreateTestSeederOptions();
            var cancellationTokenSource = new CancellationTokenSource();

            _mockSqlDataService.Setup(x => x.GenerateDriverDataAsync(
                It.IsAny<Guid>(),
                It.IsAny<int>(),
                It.IsAny<CancellationToken>()))
                .ThrowsAsync(new OperationCanceledException());

            // Act & Assert
            await Assert.ThrowsAsync<OperationCanceledException>(() =>
                service.ExecuteSeederAsync(options, cancellationTokenSource.Token));
        }

        [Fact]
        public async Task ExecuteSeederAsync_WithLargeCounts_ShouldHandleCorrectly()
        {
            // Arrange
            var service = CreateBulkSeederService();
            var options = TestConfigurationHelper.CreateTestSeederOptions(driversCount: 10000, vehiclesCount: 5000);

            SetupSuccessfulDataGeneration();

            // Act
            var result = await service.ExecuteSeederAsync(options);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Success);
            _mockSqlDataService.Verify(x => x.GenerateDriverDataAsync(
                It.IsAny<Guid>(),
                10000,
                It.IsAny<CancellationToken>()), Times.Once);
            _mockSqlDataService.Verify(x => x.GenerateVehicleDataAsync(
                It.IsAny<Guid>(),
                5000,
                It.IsAny<CancellationToken>()), Times.Once);
        }

        #endregion

        #region Progress Notification Tests

        [Fact]
        public async Task ExecuteSeederAsync_ShouldSendProgressNotifications()
        {
            // Arrange
            var service = CreateBulkSeederService();
            var options = TestConfigurationHelper.CreateTestSeederOptions();

            SetupSuccessfulDataGeneration();

            // Act
            await service.ExecuteSeederAsync(options);

            // Assert
            // Verify that SignalR notifications were sent
            var mockClientProxy = Mock.Get(_mockHubContext.Object.Clients.All);
            mockClientProxy.Verify(x => x.SendCoreAsync(
                "ProgressUpdate",
                It.IsAny<object[]>(),
                It.IsAny<CancellationToken>()), Times.AtLeastOnce);
        }

        #endregion

        #region Helper Methods

        private BulkSeederService CreateBulkSeederService()
        {
            return new BulkSeederService(
                _mockLogger.Object,
                _mockOptions.Object,
                _mockSqlDataService.Object,
                _mockEnvironmentService.Object,
                _mockHubContext.Object,
                _mockApiOrchestrationService.Object,
                _mockComplexEntityService.Object);
        }

        private void SetupSuccessfulDataGeneration()
        {
            _mockSqlDataService.Setup(x => x.GenerateDriverDataAsync(
                It.IsAny<Guid>(),
                It.IsAny<int>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(new DataGenerationResult
                {
                    Success = true,
                    GeneratedRows = 10
                });

            _mockSqlDataService.Setup(x => x.GenerateVehicleDataAsync(
                It.IsAny<Guid>(),
                It.IsAny<int>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(new DataGenerationResult
                {
                    Success = true,
                    GeneratedRows = 5
                });

            _mockSqlDataService.Setup(x => x.ProcessStagedDataAsync(
                It.IsAny<Guid>(),
                It.IsAny<bool>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(new ProcessingResult
                {
                    Success = true,
                    InsertedRows = 15,
                    UpdatedRows = 0
                });
        }

        public void Dispose()
        {
            // Cleanup any resources if needed
        }

        #endregion
    }
}
