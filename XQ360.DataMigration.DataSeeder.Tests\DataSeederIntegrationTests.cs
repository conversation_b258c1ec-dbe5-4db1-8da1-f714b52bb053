using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using System;
using System.Threading;
using System.Threading.Tasks;
using Xunit;
using XQ360.DataMigration.Models;
using XQ360.DataMigration.Services;
using XQ360.DataMigration.Web.Hubs;
using XQ360.DataMigration.Web.Models;
using XQ360.DataMigration.Web.Services.BulkSeeder;

namespace XQ360.DataMigration.DataSeeder.Tests
{
    /// <summary>
    /// Integration tests that verify the interaction between DataSeeder services and their dependencies
    /// Tests database operations, API calls, and SignalR notifications in realistic scenarios
    /// </summary>
    public class DataSeederIntegrationTests : IDisposable
    {
        private readonly ServiceProvider _serviceProvider;
        private readonly Mock<IApiOrchestrationService> _mockApiOrchestrationService;
        private readonly Mock<IComplexEntityCreationService> _mockComplexEntityService;
        // Mock<IStagingSchemaService> removed - service no longer used
        private readonly Mock<IHubContext<MigrationHub>> _mockHubContext;

        public DataSeederIntegrationTests()
        {
            // Setup mocks for external dependencies
            _mockApiOrchestrationService = new Mock<IApiOrchestrationService>();
            _mockComplexEntityService = new Mock<IComplexEntityCreationService>();
            // Mock<IStagingSchemaService> initialization removed - service no longer used
            _mockHubContext = new Mock<IHubContext<MigrationHub>>();

            // Setup SignalR hub context
            var mockClients = new Mock<IHubClients>();
            var mockClientProxy = new Mock<IClientProxy>();
            _mockHubContext.Setup(x => x.Clients).Returns(mockClients.Object);
            mockClients.Setup(x => x.All).Returns(mockClientProxy.Object);

            // Setup service collection for dependency injection
            var services = new ServiceCollection();

            // Add logging
            services.AddLogging(builder => builder.AddConsole());

            // Add configuration
            var bulkSeederConfig = TestConfigurationHelper.GetBulkSeederConfiguration();
            services.AddSingleton(Options.Create(bulkSeederConfig));

            // Add environment configuration service
            var environmentService = TestConfigurationHelper.GetEnvironmentService();
            services.AddSingleton<IEnvironmentConfigurationService>(environmentService);

            // Add mocked services
            services.AddSingleton(_mockApiOrchestrationService.Object);
            services.AddSingleton(_mockComplexEntityService.Object);
            // Mock IStagingSchemaService registration removed - service no longer used
            services.AddSingleton(_mockHubContext.Object);

            // Add real services under test
            services.AddTransient<ISqlDataGenerationService, SqlDataGenerationService>();
            services.AddTransient<IBulkSeederService, BulkSeederService>();
            services.AddTransient<IMigrationPatternSeederService, MigrationPatternSeederService>();

            _serviceProvider = services.BuildServiceProvider();
        }

        #region Service Integration Tests

        [Fact]
        public async Task BulkSeederService_IntegratedWithSqlDataService_ShouldWorkTogether()
        {
            // Arrange
            var bulkSeederService = _serviceProvider.GetRequiredService<IBulkSeederService>();
            var options = TestConfigurationHelper.CreateTestSeederOptions(driversCount: 5, vehiclesCount: 3);

            // Act
            var result = await bulkSeederService.ExecuteSeederAsync(options);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.Success); // Expected to fail due to database connectivity
            Assert.NotEmpty(result.Errors);
        }

        [Fact]
        public async Task MigrationPatternSeederService_IntegratedWithDependencies_ShouldWorkTogether()
        {
            // Arrange
            var migrationSeederService = _serviceProvider.GetRequiredService<IMigrationPatternSeederService>();
            var options = TestConfigurationHelper.CreateTestMigrationPatternOptions();
            options.UseApiForPersonCreation = true;

            // Setup successful API orchestration
            _mockApiOrchestrationService.Setup(x => x.CreatePersonDriverBatchAsync(
                It.IsAny<System.Collections.Generic.IEnumerable<PersonCreateRequest>>(),
                It.IsAny<int>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(new ApiOrchestrationResult
                {
                    Success = true,
                    SuccessfulRequests = 10,
                    TotalRequests = 10
                });

            // Act
            var result = await migrationSeederService.ExecutePersonDriverSeederAsync(options);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Success);

            // Verify API orchestration was called
            _mockApiOrchestrationService.Verify(x => x.CreatePersonDriverBatchAsync(
                It.IsAny<System.Collections.Generic.IEnumerable<PersonCreateRequest>>(),
                It.IsAny<int>(),
                It.IsAny<CancellationToken>()), Times.Once);
        }

        #endregion

        #region SignalR Integration Tests

        [Fact]
        public async Task BulkSeederService_ShouldSendSignalRNotifications()
        {
            // Arrange
            var bulkSeederService = _serviceProvider.GetRequiredService<IBulkSeederService>();
            var options = TestConfigurationHelper.CreateTestSeederOptions(driversCount: 1, vehiclesCount: 1);

            // Act
            await bulkSeederService.ExecuteSeederAsync(options);

            // Assert
            // Verify that SignalR notifications were sent
            var mockClientProxy = Mock.Get(_mockHubContext.Object.Clients.All);
            mockClientProxy.Verify(x => x.SendCoreAsync(
                "ProgressUpdate",
                It.IsAny<object[]>(),
                It.IsAny<CancellationToken>()), Times.AtLeastOnce);
        }

        [Fact]
        public async Task MigrationPatternSeederService_ShouldSendSignalRNotifications()
        {
            // Arrange
            var migrationSeederService = _serviceProvider.GetRequiredService<IMigrationPatternSeederService>();
            var options = TestConfigurationHelper.CreateTestMigrationPatternOptions(driversCount: 1);

            // Act
            await migrationSeederService.ExecutePersonDriverSeederAsync(options);

            // Assert
            // Verify that SignalR notifications were sent
            var mockClientProxy = Mock.Get(_mockHubContext.Object.Clients.All);
            mockClientProxy.Verify(x => x.SendCoreAsync(
                "ProgressUpdate",
                It.IsAny<object[]>(),
                It.IsAny<CancellationToken>()), Times.AtLeastOnce);
        }

        #endregion

        #region Configuration Integration Tests

        [Fact]
        public void Services_ShouldUseCorrectConfiguration()
        {
            // Arrange & Act
            var bulkSeederService = _serviceProvider.GetRequiredService<IBulkSeederService>();
            var migrationSeederService = _serviceProvider.GetRequiredService<IMigrationPatternSeederService>();
            var sqlDataService = _serviceProvider.GetRequiredService<ISqlDataGenerationService>();

            // Assert
            Assert.NotNull(bulkSeederService);
            Assert.NotNull(migrationSeederService);
            Assert.NotNull(sqlDataService);
        }

        [Fact]
        public void EnvironmentConfigurationService_ShouldProvideCorrectConfiguration()
        {
            // Arrange
            var environmentService = _serviceProvider.GetRequiredService<IEnvironmentConfigurationService>();

            // Act
            var currentEnvironment = environmentService.CurrentEnvironment;
            var migrationConfig = environmentService.CurrentMigrationConfiguration;

            // Assert
            Assert.NotNull(currentEnvironment);
            Assert.NotNull(migrationConfig);
            Assert.NotEmpty(currentEnvironment.DatabaseConnection);
            Assert.NotEmpty(currentEnvironment.ApiBaseUrl);
        }

        #endregion

        #region Error Handling Integration Tests

        [Fact]
        public async Task BulkSeederService_WhenSqlServiceFails_ShouldHandleGracefully()
        {
            // Arrange
            var bulkSeederService = _serviceProvider.GetRequiredService<IBulkSeederService>();
            var options = TestConfigurationHelper.CreateTestSeederOptions(driversCount: 1);

            // Act
            var result = await bulkSeederService.ExecuteSeederAsync(options);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.Success);
            Assert.NotEmpty(result.Errors);
        }

        [Fact]
        public async Task MigrationPatternSeederService_WhenApiServiceFails_ShouldHandleGracefully()
        {
            // Arrange
            var migrationSeederService = _serviceProvider.GetRequiredService<IMigrationPatternSeederService>();
            var options = TestConfigurationHelper.CreateTestMigrationPatternOptions();
            options.UseApiForPersonCreation = true;

            // Setup API orchestration to fail
            _mockApiOrchestrationService.Setup(x => x.CreatePersonDriverBatchAsync(
                It.IsAny<System.Collections.Generic.IEnumerable<PersonCreateRequest>>(),
                It.IsAny<int>(),
                It.IsAny<CancellationToken>()))
                .ThrowsAsync(new InvalidOperationException("API service failed"));

            // Act
            var result = await migrationSeederService.ExecutePersonDriverSeederAsync(options);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.Success);
            Assert.True(result.Summary.Contains("API service failed") || result.Summary.Contains("failed"));
        }

        #endregion

        #region Cancellation Integration Tests

        [Fact]
        public async Task BulkSeederService_WithCancellation_ShouldStopGracefully()
        {
            // Arrange
            var bulkSeederService = _serviceProvider.GetRequiredService<IBulkSeederService>();
            var options = TestConfigurationHelper.CreateTestSeederOptions(driversCount: 1000);
            var cancellationTokenSource = new CancellationTokenSource();

            // Cancel after a short delay
            cancellationTokenSource.CancelAfter(TimeSpan.FromMilliseconds(100));

            // Act & Assert
            await Assert.ThrowsAsync<OperationCanceledException>(() =>
                bulkSeederService.ExecuteSeederAsync(options, cancellationTokenSource.Token));
        }

        [Fact]
        public async Task MigrationPatternSeederService_WithCancellation_ShouldStopGracefully()
        {
            // Arrange
            var migrationSeederService = _serviceProvider.GetRequiredService<IMigrationPatternSeederService>();
            var options = TestConfigurationHelper.CreateTestMigrationPatternOptions();
            var cancellationTokenSource = new CancellationTokenSource();
            cancellationTokenSource.Cancel(); // Cancel immediately

            // Act & Assert
            await Assert.ThrowsAsync<OperationCanceledException>(() =>
                migrationSeederService.ExecutePersonDriverSeederAsync(options, cancellationTokenSource.Token));
        }

        #endregion

        #region Dependency Injection Tests

        [Fact]
        public void ServiceProvider_ShouldResolveAllRequiredServices()
        {
            // Act & Assert
            Assert.NotNull(_serviceProvider.GetRequiredService<IBulkSeederService>());
            Assert.NotNull(_serviceProvider.GetRequiredService<IMigrationPatternSeederService>());
            Assert.NotNull(_serviceProvider.GetRequiredService<ISqlDataGenerationService>());
            Assert.NotNull(_serviceProvider.GetRequiredService<IEnvironmentConfigurationService>());
            Assert.NotNull(_serviceProvider.GetRequiredService<IApiOrchestrationService>());
            Assert.NotNull(_serviceProvider.GetRequiredService<IComplexEntityCreationService>());
        }

        [Fact]
        public void ServiceProvider_ShouldCreateSingletonServices()
        {
            // Act
            var environmentService1 = _serviceProvider.GetRequiredService<IEnvironmentConfigurationService>();
            var environmentService2 = _serviceProvider.GetRequiredService<IEnvironmentConfigurationService>();

            // Assert
            Assert.Same(environmentService1, environmentService2);
        }

        #endregion

        public void Dispose()
        {
            _serviceProvider?.Dispose();
        }
    }
}
