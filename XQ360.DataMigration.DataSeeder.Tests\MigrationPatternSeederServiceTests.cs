using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Xunit;
using XQ360.DataMigration.Models;
using XQ360.DataMigration.Services;
using XQ360.DataMigration.Web.Hubs;
using XQ360.DataMigration.Web.Models;
using XQ360.DataMigration.Web.Services.BulkSeeder;

namespace XQ360.DataMigration.DataSeeder.Tests
{
    /// <summary>
    /// Comprehensive unit tests for MigrationPatternSeederService
    /// Tests Person/Driver seeding, Vehicle seeding, Card/Access seeding, full migration patterns, and validation methods
    /// </summary>
    public class MigrationPatternSeederServiceTests : IDisposable
    {
        private readonly Mock<ILogger<MigrationPatternSeederService>> _mockLogger;
        private readonly Mock<IOptions<BulkSeederConfiguration>> _mockOptions;
        private readonly Mock<ISqlDataGenerationService> _mockSqlDataService;
        private readonly Mock<IEnvironmentConfigurationService> _mockEnvironmentService;
        private readonly Mock<IHubContext<MigrationHub>> _mockHubContext;
        private readonly Mock<IApiOrchestrationService> _mockApiOrchestrationService;
        private readonly Mock<IComplexEntityCreationService> _mockComplexEntityService;
        // Mock<IStagingSchemaService> removed - service no longer used
        private readonly BulkSeederConfiguration _testConfig;

        public MigrationPatternSeederServiceTests()
        {
            _mockLogger = new Mock<ILogger<MigrationPatternSeederService>>();
            _mockOptions = new Mock<IOptions<BulkSeederConfiguration>>();
            _mockSqlDataService = new Mock<ISqlDataGenerationService>();
            _mockEnvironmentService = new Mock<IEnvironmentConfigurationService>();
            _mockHubContext = new Mock<IHubContext<MigrationHub>>();
            _mockApiOrchestrationService = new Mock<IApiOrchestrationService>();
            _mockComplexEntityService = new Mock<IComplexEntityCreationService>();
            // Mock<IStagingSchemaService> initialization removed - service no longer used

            _testConfig = TestConfigurationHelper.GetBulkSeederConfiguration();
            _mockOptions.Setup(x => x.Value).Returns(_testConfig);

            // Setup environment service
            var testEnvironment = TestConfigurationHelper.GetTestEnvironment();
            var testMigrationConfig = TestConfigurationHelper.GetTestConfiguration();
            _mockEnvironmentService.Setup(x => x.CurrentEnvironment).Returns(testEnvironment);
            _mockEnvironmentService.Setup(x => x.CurrentMigrationConfiguration).Returns(testMigrationConfig);

            // Setup SignalR hub context
            var mockClients = new Mock<IHubClients>();
            var mockClientProxy = new Mock<IClientProxy>();
            var mockGroupManager = new Mock<IGroupManager>();
            _mockHubContext.Setup(x => x.Clients).Returns(mockClients.Object);
            _mockHubContext.Setup(x => x.Groups).Returns(mockGroupManager.Object);
            mockClients.Setup(x => x.All).Returns(mockClientProxy.Object);
            mockClients.Setup(x => x.Group(It.IsAny<string>())).Returns(mockClientProxy.Object);
        }

        #region Constructor Tests

        [Fact]
        public void Constructor_WithValidParameters_ShouldCreateInstance()
        {
            // Act
            var service = CreateMigrationPatternSeederService();

            // Assert
            Assert.NotNull(service);
        }

        [Fact]
        public void Constructor_WithNullLogger_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new MigrationPatternSeederService(
                null!,
                _mockOptions.Object,
                _mockSqlDataService.Object,
                _mockEnvironmentService.Object,
                _mockHubContext.Object,
                _mockApiOrchestrationService.Object,
                _mockComplexEntityService.Object
                // _mockStagingSchemaService.Object parameter removed
                ));
        }

        [Fact]
        public void Constructor_WithNullApiOrchestrationService_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new MigrationPatternSeederService(
                _mockLogger.Object,
                _mockOptions.Object,
                _mockSqlDataService.Object,
                _mockEnvironmentService.Object,
                _mockHubContext.Object,
                null!,
                _mockComplexEntityService.Object
                // _mockStagingSchemaService.Object parameter removed
                ));
        }

        [Fact]
        public void Constructor_WithNullComplexEntityService_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new MigrationPatternSeederService(
                _mockLogger.Object,
                _mockOptions.Object,
                _mockSqlDataService.Object,
                _mockEnvironmentService.Object,
                _mockHubContext.Object,
                _mockApiOrchestrationService.Object,
                null!
                // _mockStagingSchemaService.Object parameter removed
                ));
        }

        #endregion

        #region ExecutePersonDriverSeederAsync Tests

        [Fact]
        public async Task ExecutePersonDriverSeederAsync_WithValidOptions_ShouldReturnSuccessResult()
        {
            // Arrange
            var service = CreateMigrationPatternSeederService();
            var options = TestConfigurationHelper.CreateTestMigrationPatternOptions();

            SetupSuccessfulApiOrchestration();

            // Act
            var result = await service.ExecutePersonDriverSeederAsync(options);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Success);
            Assert.NotEmpty(result.SessionId);
            Assert.True(result.Duration > TimeSpan.Zero);
        }

        [Fact]
        public async Task ExecutePersonDriverSeederAsync_WithDriversCount_ShouldCallApiOrchestration()
        {
            // Arrange
            var service = CreateMigrationPatternSeederService();
            var options = TestConfigurationHelper.CreateTestMigrationPatternOptions(driversCount: 25);
            options.UseApiForPersonCreation = true;

            SetupSuccessfulApiOrchestration();

            // Act
            await service.ExecutePersonDriverSeederAsync(options);

            // Assert
            _mockApiOrchestrationService.Verify(x => x.CreatePersonDriverBatchAsync(
                It.Is<IEnumerable<PersonCreateRequest>>(requests => requests.Count() == 25),
                It.IsAny<int>(),
                It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task ExecutePersonDriverSeederAsync_WithZeroDriversCount_ShouldReturnSuccessWithoutApiCall()
        {
            // Arrange
            var service = CreateMigrationPatternSeederService();
            var options = TestConfigurationHelper.CreateTestMigrationPatternOptions(driversCount: 0);

            // Act
            var result = await service.ExecutePersonDriverSeederAsync(options);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Success);
            Assert.Contains("No Person/Driver records to create", result.Summary);
            _mockApiOrchestrationService.Verify(x => x.CreatePersonDriverBatchAsync(
                It.IsAny<IEnumerable<PersonCreateRequest>>(),
                It.IsAny<int>(),
                It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task ExecutePersonDriverSeederAsync_WhenApiOrchestrationFails_ShouldReturnFailureResult()
        {
            // Arrange
            var service = CreateMigrationPatternSeederService();
            var options = TestConfigurationHelper.CreateTestMigrationPatternOptions();

            _mockApiOrchestrationService.Setup(x => x.CreatePersonDriverBatchAsync(
                It.IsAny<IEnumerable<PersonCreateRequest>>(),
                It.IsAny<int>(),
                It.IsAny<CancellationToken>()))
                .ThrowsAsync(new InvalidOperationException("API connection failed"));

            // Act
            var result = await service.ExecutePersonDriverSeederAsync(options);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.Success);
            Assert.True(result.Summary.Contains("API connection failed") || result.Summary.Contains("failed"));
        }

        #endregion

        #region ExecuteVehicleSeederAsync Tests

        [Fact]
        public async Task ExecuteVehicleSeederAsync_WithValidOptions_ShouldReturnSuccessResult()
        {
            // Arrange
            var service = CreateMigrationPatternSeederService();
            var options = TestConfigurationHelper.CreateTestMigrationPatternOptions();

            SetupSuccessfulComplexEntityCreation();

            // Act
            var result = await service.ExecuteVehicleSeederAsync(options);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Success);
            Assert.NotEmpty(result.SessionId);
        }

        [Fact]
        public async Task ExecuteVehicleSeederAsync_WithVehiclesCount_ShouldCallComplexEntityService()
        {
            // Arrange
            var service = CreateMigrationPatternSeederService();
            var options = TestConfigurationHelper.CreateTestMigrationPatternOptions(vehiclesCount: 15);

            SetupSuccessfulComplexEntityCreation();

            // Act
            await service.ExecuteVehicleSeederAsync(options);

            // Assert
            _mockComplexEntityService.Verify(x => x.CreateVehicleBatchAsync(
                It.IsAny<Guid>(),
                It.Is<IEnumerable<VehicleCreateRequest>>(requests => requests.Count() == 15),
                It.IsAny<CancellationToken>()), Times.Once);
        }

        #endregion

        #region ExecuteCardAccessSeederAsync Tests

        [Fact]
        public async Task ExecuteCardAccessSeederAsync_WithValidOptions_ShouldReturnSuccessResult()
        {
            // Arrange
            var service = CreateMigrationPatternSeederService();
            var options = TestConfigurationHelper.CreateTestMigrationPatternOptions();

            SetupSuccessfulComplexEntityCreation();

            // Act
            var result = await service.ExecuteCardAccessSeederAsync(options);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Success);
            Assert.NotEmpty(result.SessionId);
        }

        [Fact]
        public async Task ExecuteCardAccessSeederAsync_WithDriversCount_ShouldCallComplexEntityService()
        {
            // Arrange
            var service = CreateMigrationPatternSeederService();
            var options = TestConfigurationHelper.CreateTestMigrationPatternOptions(driversCount: 20);

            SetupSuccessfulComplexEntityCreation();

            // Act
            await service.ExecuteCardAccessSeederAsync(options);

            // Assert
            _mockComplexEntityService.Verify(x => x.CreateCardAccessPermissionsBatchAsync(
                It.IsAny<Guid>(),
                It.Is<IEnumerable<CardAccessCreateRequest>>(requests => requests.Count() == 20),
                It.IsAny<CancellationToken>()), Times.Once);
        }

        #endregion

        #region ExecuteFullMigrationPatternAsync Tests

        [Fact]
        public async Task ExecuteFullMigrationPatternAsync_WithValidOptions_ShouldReturnSuccessResult()
        {
            // Arrange
            var service = CreateMigrationPatternSeederService();
            var options = TestConfigurationHelper.CreateTestMigrationPatternOptions();
            options.UseApiForPersonCreation = true;
            options.UseComplexVehicleCreation = true;
            options.CreateCardAccessPermissions = true;

            SetupSuccessfulApiOrchestration();
            SetupSuccessfulComplexEntityCreation();

            // Act
            var result = await service.ExecuteFullMigrationPatternAsync(options);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Success);
            Assert.NotEmpty(result.SessionId);
        }

        [Fact]
        public async Task ExecuteFullMigrationPatternAsync_WhenPersonCreationFails_ShouldStopAndReturnFailure()
        {
            // Arrange
            var service = CreateMigrationPatternSeederService();
            var options = TestConfigurationHelper.CreateTestMigrationPatternOptions();
            options.UseApiForPersonCreation = true;

            _mockApiOrchestrationService.Setup(x => x.CreatePersonDriverBatchAsync(
                It.IsAny<IEnumerable<PersonCreateRequest>>(),
                It.IsAny<int>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(new ApiOrchestrationResult { Success = false });

            // Act
            var result = await service.ExecuteFullMigrationPatternAsync(options);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.Success);
            Assert.Contains("Person/Driver creation failed - stopping migration", result.Errors);
        }

        [Fact]
        public async Task ExecuteFullMigrationPatternAsync_WithDisabledSteps_ShouldSkipThoseSteps()
        {
            // Arrange
            var service = CreateMigrationPatternSeederService();
            var options = TestConfigurationHelper.CreateTestMigrationPatternOptions();
            options.UseApiForPersonCreation = false;
            options.UseComplexVehicleCreation = false;
            options.CreateCardAccessPermissions = false;

            // Act
            var result = await service.ExecuteFullMigrationPatternAsync(options);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Success);
            _mockApiOrchestrationService.Verify(x => x.CreatePersonDriverBatchAsync(
                It.IsAny<IEnumerable<PersonCreateRequest>>(),
                It.IsAny<int>(),
                It.IsAny<CancellationToken>()), Times.Never);
        }

        #endregion

        #region ValidateMigrationPatternPrerequisitesAsync Tests

        [Fact]
        public async Task ValidateMigrationPatternPrerequisitesAsync_WithValidSetup_ShouldReturnValidResult()
        {
            // Arrange
            var service = CreateMigrationPatternSeederService();

            _mockApiOrchestrationService.Setup(x => x.ValidateApiConnectivityAsync())
                .ReturnsAsync(true);

            // Act
            var result = await service.ValidateMigrationPatternPrerequisitesAsync();

            // Assert
            Assert.NotNull(result);
            Assert.True(result.IsValid);
            Assert.True(result.ApiConnectivityValid);
        }

        [Fact]
        public async Task ValidateMigrationPatternPrerequisitesAsync_WithInvalidApi_ShouldReturnInvalidResult()
        {
            // Arrange
            var service = CreateMigrationPatternSeederService();

            _mockApiOrchestrationService.Setup(x => x.ValidateApiConnectivityAsync())
                .ReturnsAsync(false);

            // Act
            var result = await service.ValidateMigrationPatternPrerequisitesAsync();

            // Assert
            Assert.NotNull(result);
            Assert.False(result.IsValid);
            Assert.False(result.ApiConnectivityValid);
            Assert.NotEmpty(result.Errors);
        }

        [Fact]
        public async Task ValidateMigrationPatternPrerequisitesAsync_WhenApiThrows_ShouldReturnInvalidResult()
        {
            // Arrange
            var service = CreateMigrationPatternSeederService();

            _mockApiOrchestrationService.Setup(x => x.ValidateApiConnectivityAsync())
                .ThrowsAsync(new InvalidOperationException("API validation failed"));

            // Act
            var result = await service.ValidateMigrationPatternPrerequisitesAsync();

            // Assert
            Assert.NotNull(result);
            Assert.False(result.IsValid);
            Assert.Contains("Validation failed: API validation failed", result.Errors);
        }

        #endregion

        #region Edge Cases and Error Handling Tests

        [Fact]
        public async Task ExecutePersonDriverSeederAsync_WithNullDriversCount_ShouldReturnSuccessWithoutApiCall()
        {
            // Arrange
            var service = CreateMigrationPatternSeederService();
            var options = TestConfigurationHelper.CreateTestMigrationPatternOptions(driversCount: null);

            // Act
            var result = await service.ExecutePersonDriverSeederAsync(options);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Success);
            _mockApiOrchestrationService.Verify(x => x.CreatePersonDriverBatchAsync(
                It.IsAny<IEnumerable<PersonCreateRequest>>(),
                It.IsAny<int>(),
                It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task ExecuteVehicleSeederAsync_WithZeroVehiclesCount_ShouldReturnSuccessWithoutCreation()
        {
            // Arrange
            var service = CreateMigrationPatternSeederService();
            var options = TestConfigurationHelper.CreateTestMigrationPatternOptions(vehiclesCount: 0);

            // Act
            var result = await service.ExecuteVehicleSeederAsync(options);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Success);
            Assert.Contains("No Vehicle records to create", result.Summary);
        }

        [Fact]
        public async Task ExecuteCardAccessSeederAsync_WithZeroDriversCount_ShouldReturnSuccessWithoutCreation()
        {
            // Arrange
            var service = CreateMigrationPatternSeederService();
            var options = TestConfigurationHelper.CreateTestMigrationPatternOptions(driversCount: 0);

            // Act
            var result = await service.ExecuteCardAccessSeederAsync(options);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Success);
            Assert.Contains("No Card/Access records to create", result.Summary);
        }

        #endregion

        #region Helper Methods

        private MigrationPatternSeederService CreateMigrationPatternSeederService()
        {
            return new MigrationPatternSeederService(
                _mockLogger.Object,
                _mockOptions.Object,
                _mockSqlDataService.Object,
                _mockEnvironmentService.Object,
                _mockHubContext.Object,
                _mockApiOrchestrationService.Object,
                _mockComplexEntityService.Object
                // _mockStagingSchemaService.Object parameter removed
                );
        }

        private void SetupSuccessfulApiOrchestration()
        {
            _mockApiOrchestrationService.Setup(x => x.CreatePersonDriverBatchAsync(
                It.IsAny<IEnumerable<PersonCreateRequest>>(),
                It.IsAny<int>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(new ApiOrchestrationResult
                {
                    Success = true,
                    SuccessfulRequests = 10,
                    TotalRequests = 10
                });
        }

        private void SetupSuccessfulComplexEntityCreation()
        {
            _mockComplexEntityService.Setup(x => x.CreateVehicleBatchAsync(
                It.IsAny<Guid>(),
                It.IsAny<IEnumerable<VehicleCreateRequest>>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(new ComplexEntityResult
                {
                    Success = true,
                    SuccessfulRequests = 5,
                    TotalRequests = 5
                });

            _mockComplexEntityService.Setup(x => x.CreateCardAccessPermissionsBatchAsync(
                It.IsAny<Guid>(),
                It.IsAny<IEnumerable<CardAccessCreateRequest>>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(new ComplexEntityResult
                {
                    Success = true,
                    SuccessfulRequests = 10,
                    TotalRequests = 10
                });
        }

        public void Dispose()
        {
            // Cleanup any resources if needed
        }

        #endregion
    }
}
